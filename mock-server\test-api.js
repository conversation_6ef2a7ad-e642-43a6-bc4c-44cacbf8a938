const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testAPI() {
  try {
    console.log('🧪 测试Mock API接口...\n');

    // 1. 测试登录
    console.log('1. 测试登录...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      username: 'testuser',
      password: 'test123'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ 登录成功');
      const token = loginResponse.data.data.token;
      
      // 2. 测试获取套餐
      console.log('2. 测试获取套餐...');
      const packagesResponse = await axios.get(`${BASE_URL}/packages`);
      console.log(`✅ 获取到 ${packagesResponse.data.data.length} 个套餐`);
      
      // 3. 测试更新用户名
      console.log('3. 测试更新用户名...');
      const profileResponse = await axios.put(`${BASE_URL}/user/profile`, {
        username: 'testuser_updated'
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ 用户名更新成功');
      
      // 4. 测试更新密码
      console.log('4. 测试更新密码...');
      const passwordResponse = await axios.put(`${BASE_URL}/user/password`, {
        oldPassword: 'test123',
        newPassword: 'newpassword123'
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ 密码更新成功');
      
      // 5. 测试更新设置
      console.log('5. 测试更新设置...');
      const settingsResponse = await axios.put(`${BASE_URL}/user/settings`, {
        enableBiometric: true,
        autoSync: false
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ 设置更新成功');
      
      // 6. 测试数据同步
      console.log('6. 测试数据同步...');
      const syncResponse = await axios.post(`${BASE_URL}/sync/upload`, {
        data: { test: 'data' }
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ 数据同步成功');
      
      console.log('\n🎉 所有API测试通过！');
      
    } else {
      console.log('❌ 登录失败');
    }
    
  } catch (error) {
    console.error('❌ API测试失败:', error.response?.data || error.message);
    console.error('请求URL:', error.config?.url);
    console.error('请求方法:', error.config?.method);
  }
}

// 检查是否作为脚本运行
if (require.main === module) {
  testAPI();
}

module.exports = testAPI;
