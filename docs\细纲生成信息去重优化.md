# 细纲生成信息去重优化

## 问题发现

在实现完整故事大纲功能后，发现细纲生成存在严重的信息重复问题：

### 🚨 **重复问题分析**

#### 1. **章节信息的三重重复**
- **`fullOutline` 参数**：包含所有章节的基础信息
- **`history` 参数**：包含已生成章节的详细细纲
- **`previousChapterInfo` 参数**：包含前一章的详细信息

#### 2. **具体重复内容**
```
第1章信息出现在：
├── fullOutline: "第1章：标题1 概要：..."
├── history: "第1章：标题1（前文章节-已发生）[完整细纲]"
└── previousChapterInfo: "前一章详细细纲: 第1章 [完整细纲]"
```

#### 3. **问题影响**
- **Token浪费**：大量重复信息消耗API调用成本
- **上下文混乱**：AI可能被重复信息干扰
- **生成质量下降**：重要信息被稀释

## 解决方案

### 🔧 **信息去重策略**

#### 1. **职责分离**
- **`fullOutline`**：只包含**尚未生成详细细纲**的章节
- **`history`**：包含**已生成详细细纲**的章节
- **移除重复参数**：删除 `previousChapterInfo` 和 `nextChapterInfo`

#### 2. **实现逻辑**
```dart
// 获取已生成详细细纲的章节信息
final allDetailedChapters = await novelMemory.getAllChapters();

// 构建去重的完整大纲（排除已有详细细纲的章节）
String fullOutlineString = _buildFullOutlineStringWithoutDetailed(
  fullOutline,
  allDetailedChapters.keys.toSet()
);

// 移除重复的前后章节信息参数
// previousChapterInfo 和 nextChapterInfo 已被移除
```

#### 3. **去重后的信息结构**

##### **`fullOutline` 参数（去重后）**
```
=== 完整故事发展脉络 ===
小说标题：某某小说
计划总章节数：50

### 章节发展脉络：
**第5章：未来章节**     ← 只包含尚未生成详细细纲的章节
概要：后续情节...

**第6章：未来章节**
概要：后续情节...
```

##### **`history` 参数（通过Memory）**
```
=== 已生成的章节细纲和关键事件时间线 ===

### 关键事件时间线（已发生）：
- 第1章：关键事件概要
- 第2章：关键事件概要

### 第1章：已完成章节（前文章节 - 已发生）
**状态：已完成，不可重复**
[完整的详细细纲内容]

### 第2章：已完成章节（前文章节 - 已发生）
**状态：已完成，不可重复**
[完整的详细细纲内容]
```

## 修改的文件

### 1. **服务层修改**
- `lib/langchain/services/lightweight_generation_service.dart`
- `lib/langchain/services/novel_generation_service.dart`

**主要变更：**
- 添加 `_buildFullOutlineStringWithoutDetailed()` 方法
- 移除 `previousChapterInfo` 和 `nextChapterInfo` 参数
- 实现章节信息去重逻辑

### 2. **提示词模板修改**
- `lib/langchain/prompts/novel_prompt_templates_enhanced.dart`

**主要变更：**
- 添加信息说明，明确两个参数的职责分工
- 强调避免重复已生成的情节

### 3. **文档更新**
- `docs/细纲生成输入参数详解.md`
- `docs/细纲生成信息去重优化.md`

## 优化效果

### ✅ **信息结构清晰**
- **已生成章节**：完整详细信息在 `history` 中
- **未生成章节**：基础规划信息在 `fullOutline` 中
- **当前章节**：基本信息在专门参数中

### ✅ **避免重复**
- 每个章节的信息只在一个地方出现
- 不同类型的信息有明确的来源

### ✅ **Token优化**
- 减少重复信息的传输
- 提高API调用效率

### ✅ **逻辑清晰**
- AI能更清楚地理解信息结构
- 减少因重复信息导致的混乱

## 使用示例

### **生成第4章细纲时的输入结构**

```dart
{
  // 基础信息
  'novelTitle': '某某小说',
  'chapterNumber': '4',
  'chapterTitle': '第四章标题',
  'chapterSummary': '第四章概要',
  
  // 完整故事大纲（只包含第5章及以后）
  'fullOutline': '''
  === 完整故事发展脉络 ===
  ### 章节发展脉络：
  **第5章：未来发展**
  概要：后续情节...
  **第6章：高潮部分**
  概要：故事高潮...
  ''',
  
  // 历史上下文（通过Memory自动注入，包含第1-3章详细细纲）
  'history': '''
  === 已生成的章节细纲和关键事件时间线 ===
  ### 第1章：开端（前文章节 - 已发生）
  **状态：已完成，不可重复**
  [第1章完整细纲]
  ### 第2章：发展（前文章节 - 已发生）
  **状态：已完成，不可重复**
  [第2章完整细纲]
  ### 第3章：转折（前文章节 - 已发生）
  **状态：已完成，不可重复**
  [第3章完整细纲]
  '''
}
```

## 验证方法

### **检查去重效果**
1. 查看生成日志，确认参数构建正确
2. 检查 `fullOutline` 是否只包含未生成细纲的章节
3. 验证 `history` 是否包含所有已生成的详细细纲
4. 确认没有章节信息在多个参数中重复出现

### **测试连贯性**
1. 连续生成多个章节的细纲
2. 验证后续章节不会重复前面的情节
3. 检查时间线和角色状态的连贯性
4. 确认整体故事发展的一致性

## 总结

通过信息去重优化，细纲生成系统现在能够：
- **高效传递信息**：避免重复，提高效率
- **清晰区分职责**：不同参数有明确的作用
- **保持连贯性**：完整的历史上下文和未来规划
- **优化成本**：减少不必要的Token消耗

这个优化确保了AI能够在充分理解故事全貌的同时，避免信息重复带来的干扰，从而生成更高质量、更连贯的细纲内容。
