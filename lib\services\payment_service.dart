import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:fluwx/fluwx.dart';
import '../models/order.dart';
import '../models/package.dart';
import '../models/user.dart';
import '../config/api_config.dart';
import 'auth_service.dart';

/// 支付服务
class PaymentService extends GetxService {
  final Dio _dio = Dio();
  final AuthService _authService = Get.find<AuthService>();
  
  final RxList<MembershipPackage> packages = <MembershipPackage>[].obs;
  final RxList<Order> orders = <Order>[].obs;
  final RxBool isLoading = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeWechatPay();
    await loadPackages();
    await loadUserOrders();
  }

  /// 初始化微信支付
  Future<void> _initializeWechatPay() async {
    try {
      await registerWxApi(
        appId: "your_wechat_app_id", // 替换为实际的微信AppID
        doOnAndroid: true,
        doOnIOS: true,
        universalLink: "your_universal_link", // iOS需要
      );
    } catch (e) {
      print('初始化微信支付失败: $e');
    }
  }

  /// 加载会员套餐
  Future<void> loadPackages() async {
    try {
      isLoading.value = true;
      final response = await _dio.get('${ApiConfig.baseUrl}/packages');
      
      if (response.data['success'] == true) {
        final packageList = (response.data['data'] as List)
            .map((json) => MembershipPackage.fromJson(json))
            .where((pkg) => pkg.isActive)
            .toList();
        
        packageList.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
        packages.assignAll(packageList);
      }
    } catch (e) {
      print('加载套餐失败: $e');
      Get.snackbar('错误', '加载套餐失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载用户订单
  Future<void> loadUserOrders() async {
    if (!_authService.isLoggedIn.value) return;
    
    try {
      final response = await _dio.get('${ApiConfig.baseUrl}/orders/my');
      
      if (response.data['success'] == true) {
        final orderList = (response.data['data'] as List)
            .map((json) => Order.fromJson(json))
            .toList();
        
        orderList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        orders.assignAll(orderList);
      }
    } catch (e) {
      print('加载订单失败: $e');
    }
  }

  /// 创建订单
  Future<Order?> createOrder(String packageId) async {
    if (!_authService.isLoggedIn.value) {
      Get.snackbar('错误', '请先登录');
      return null;
    }

    try {
      isLoading.value = true;
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/orders/create',
        data: {'packageId': packageId},
      );

      if (response.data['success'] == true) {
        final order = Order.fromJson(response.data['data']);
        orders.insert(0, order);
        return order;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '创建订单失败');
        return null;
      }
    } catch (e) {
      print('创建订单失败: $e');
      Get.snackbar('错误', '创建订单失败: ${e.toString()}');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// 微信支付
  Future<bool> payWithWechat(String orderId) async {
    try {
      isLoading.value = true;
      
      // 获取微信支付参数
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/payment/wechat',
        data: {'orderId': orderId},
      );

      if (response.data['success'] == true) {
        final paymentData = response.data['data'];
        
        // 调用微信支付
        final result = await payWithWeChat(
          appId: paymentData['appId'],
          partnerId: paymentData['partnerId'],
          prepayId: paymentData['prepayId'],
          packageValue: paymentData['package'],
          nonceStr: paymentData['nonceStr'],
          timeStamp: paymentData['timeStamp'],
          sign: paymentData['sign'],
        );

        if (result.isSuccessful) {
          // 支付成功，查询订单状态
          await _checkPaymentStatus(orderId);
          return true;
        } else {
          Get.snackbar('支付失败', result.errorMsg ?? '微信支付失败');
          return false;
        }
      } else {
        Get.snackbar('错误', response.data['message'] ?? '获取支付参数失败');
        return false;
      }
    } catch (e) {
      print('微信支付失败: $e');
      Get.snackbar('错误', '微信支付失败: ${e.toString()}');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 会员码支付
  Future<bool> payWithMemberCode(String orderId, String memberCode) async {
    try {
      isLoading.value = true;
      
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/payment/member-code',
        data: {
          'orderId': orderId,
          'memberCode': memberCode,
        },
      );

      if (response.data['success'] == true) {
        // 会员码支付成功，更新订单状态
        await _updateOrderStatus(orderId, OrderStatus.paid);
        
        // 更新用户会员状态
        await _updateUserMembership(response.data['data']);
        
        Get.snackbar('成功', '会员码激活成功！');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '会员码无效');
        return false;
      }
    } catch (e) {
      print('会员码支付失败: $e');
      Get.snackbar('错误', '会员码支付失败: ${e.toString()}');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 检查支付状态
  Future<void> _checkPaymentStatus(String orderId) async {
    try {
      final response = await _dio.get('${ApiConfig.baseUrl}/payment/status/$orderId');
      
      if (response.data['success'] == true) {
        final orderData = response.data['data'];
        await _updateOrderStatus(orderId, OrderStatus.values.byName(orderData['status']));
        
        if (orderData['status'] == 'paid') {
          // 支付成功，更新用户会员状态
          await _updateUserMembership(orderData['membership']);
          Get.snackbar('成功', '支付成功！会员已激活');
        }
      }
    } catch (e) {
      print('检查支付状态失败: $e');
    }
  }

  /// 更新订单状态
  Future<void> _updateOrderStatus(String orderId, OrderStatus status) async {
    final orderIndex = orders.indexWhere((o) => o.id == orderId);
    if (orderIndex != -1) {
      orders[orderIndex].status = status;
      if (status == OrderStatus.paid) {
        orders[orderIndex].paidAt = DateTime.now();
      }
      orders.refresh();
    }
  }

  /// 更新用户会员状态
  Future<void> _updateUserMembership(Map<String, dynamic> membershipData) async {
    final currentUser = _authService.currentUser.value;
    if (currentUser != null) {
      currentUser.isMember = membershipData['isMember'] ?? true;
      currentUser.isPermanentMember = membershipData['isPermanent'] ?? false;
      
      if (membershipData['expireTime'] != null) {
        currentUser.memberExpireTime = DateTime.parse(membershipData['expireTime']);
      }
      
      if (membershipData['membershipType'] != null) {
        currentUser.membershipType = MembershipType.values.byName(membershipData['membershipType']);
      }
      
      _authService.currentUser.refresh();
      
      // 保存到本地
      final prefs = Get.find<SharedPreferences>();
      await prefs.setString('user_data', jsonEncode(currentUser.toJson()));
    }
  }

  /// 验证会员码
  Future<bool> validateMemberCode(String code) async {
    try {
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/member-code/validate',
        data: {'code': code},
      );
      
      return response.data['success'] == true;
    } catch (e) {
      print('验证会员码失败: $e');
      return false;
    }
  }

  /// 取消订单
  Future<bool> cancelOrder(String orderId) async {
    try {
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/orders/$orderId/cancel',
      );

      if (response.data['success'] == true) {
        await _updateOrderStatus(orderId, OrderStatus.cancelled);
        Get.snackbar('成功', '订单已取消');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '取消订单失败');
        return false;
      }
    } catch (e) {
      print('取消订单失败: $e');
      Get.snackbar('错误', '取消订单失败: ${e.toString()}');
      return false;
    }
  }

  /// 获取用户当前会员限制
  MembershipLimits getCurrentLimits() {
    final user = _authService.currentUser.value;
    if (user == null || !user.isValidMember) {
      return FreeLimits.free;
    }
    
    // 根据用户会员类型返回对应限制
    // 这里可以从服务器获取或使用默认配置
    return const MembershipLimits();
  }

  /// 检查功能是否可用
  bool canUseFeature(String feature) {
    final limits = getCurrentLimits();
    
    switch (feature) {
      case 'extended_features':
        return limits.canUseExtendedFeatures;
      case 'advanced_ai':
        return limits.canUseAdvancedAI;
      case 'multiple_formats':
        return limits.canExportToMultipleFormats;
      default:
        return true;
    }
  }

  /// 检查是否超出使用限制
  bool isWithinLimits(String limitType, int currentCount) {
    final limits = getCurrentLimits();
    
    switch (limitType) {
      case 'chapters_per_novel':
        return limits.maxChaptersPerNovel == -1 || currentCount < limits.maxChaptersPerNovel;
      case 'knowledge_documents':
        return limits.maxKnowledgeDocuments == -1 || currentCount < limits.maxKnowledgeDocuments;
      case 'novels_per_day':
        return limits.maxNovelsPerDay == -1 || currentCount < limits.maxNovelsPerDay;
      case 'custom_character_types':
        return limits.maxCustomCharacterTypes == -1 || currentCount < limits.maxCustomCharacterTypes;
      default:
        return true;
    }
  }
}
