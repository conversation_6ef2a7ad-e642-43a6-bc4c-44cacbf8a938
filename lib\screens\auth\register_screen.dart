import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../models/user.dart';
import '../../services/auth_service.dart';
import '../../services/payment_service.dart';
import 'login_screen.dart';

/// 注册页面
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _codeController = TextEditingController();
  final _memberCodeController = TextEditingController();
  
  final AuthService _authService = Get.find<AuthService>();
  final PaymentService _paymentService = Get.find<PaymentService>();
  
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false;
  bool _codeSent = false;
  int _countdown = 0;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneController.dispose();
    _codeController.dispose();
    _memberCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('注册账号'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 用户名输入框
                TextFormField(
                  controller: _usernameController,
                  decoration: InputDecoration(
                    labelText: '用户名',
                    prefixIcon: const Icon(Icons.person_outline),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    helperText: '2-20个字符，支持中文、英文、数字',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入用户名';
                    }
                    if (value.trim().length < 2 || value.trim().length > 20) {
                      return '用户名长度应在2-20个字符之间';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // 手机号输入框
                TextFormField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(11),
                  ],
                  decoration: InputDecoration(
                    labelText: '手机号',
                    prefixIcon: const Icon(Icons.phone_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入手机号';
                    }
                    if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value.trim())) {
                      return '请输入正确的手机号';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // 验证码输入框
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _codeController,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(6),
                        ],
                        decoration: InputDecoration(
                          labelText: '验证码',
                          prefixIcon: const Icon(Icons.sms_outlined),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入验证码';
                          }
                          if (value.trim().length != 6) {
                            return '验证码为6位数字';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: 120,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _countdown > 0 ? null : _sendVerificationCode,
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          _countdown > 0 ? '${_countdown}s' : '发送验证码',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // 密码输入框
                TextFormField(
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  decoration: InputDecoration(
                    labelText: '密码',
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword ? Icons.visibility_off : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    helperText: '至少6位字符',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入密码';
                    }
                    if (value.length < 6) {
                      return '密码长度不能少于6位';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // 确认密码输入框
                TextFormField(
                  controller: _confirmPasswordController,
                  obscureText: _obscureConfirmPassword,
                  decoration: InputDecoration(
                    labelText: '确认密码',
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureConfirmPassword = !_obscureConfirmPassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请确认密码';
                    }
                    if (value != _passwordController.text) {
                      return '两次输入的密码不一致';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // 会员码输入框（可选）
                TextFormField(
                  controller: _memberCodeController,
                  decoration: InputDecoration(
                    labelText: '会员码（可选）',
                    prefixIcon: const Icon(Icons.card_giftcard_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    helperText: '输入会员码可获得永久会员',
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // 用户协议
                Row(
                  children: [
                    Checkbox(
                      value: _agreeToTerms,
                      onChanged: (value) {
                        setState(() {
                          _agreeToTerms = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                      child: Wrap(
                        children: [
                          const Text('我已阅读并同意'),
                          TextButton(
                            onPressed: () {
                              // TODO: 显示用户协议
                            },
                            child: const Text('《用户协议》'),
                          ),
                          const Text('和'),
                          TextButton(
                            onPressed: () {
                              // TODO: 显示隐私政策
                            },
                            child: const Text('《隐私政策》'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // 注册按钮
                Obx(() => SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _authService.isLoading.value || !_agreeToTerms 
                        ? null 
                        : _handleRegister,
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _authService.isLoading.value
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                            '注册',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                  ),
                )),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 发送验证码
  Future<void> _sendVerificationCode() async {
    final phone = _phoneController.text.trim();
    if (phone.isEmpty || !RegExp(r'^1[3-9]\d{9}$').hasMatch(phone)) {
      Get.snackbar('错误', '请输入正确的手机号');
      return;
    }

    final success = await _authService.sendVerificationCode(phone);
    if (success) {
      setState(() {
        _codeSent = true;
        _countdown = 60;
      });
      _startCountdown();
      Get.snackbar('成功', '验证码已发送');
    }
  }

  /// 开始倒计时
  void _startCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (_countdown > 0) {
        setState(() {
          _countdown--;
        });
        _startCountdown();
      }
    });
  }

  /// 处理注册
  Future<void> _handleRegister() async {
    if (!_formKey.currentState!.validate()) return;

    // 验证验证码
    final codeValid = await _authService.verifyCode(
      _phoneController.text.trim(),
      _codeController.text.trim(),
    );

    if (!codeValid) {
      Get.snackbar('错误', '验证码错误或已过期');
      return;
    }

    // 验证会员码（如果填写了）
    String? memberCode;
    if (_memberCodeController.text.trim().isNotEmpty) {
      final memberCodeValid = await _paymentService.validateMemberCode(
        _memberCodeController.text.trim(),
      );
      if (!memberCodeValid) {
        Get.snackbar('错误', '会员码无效');
        return;
      }
      memberCode = _memberCodeController.text.trim();
    }

    // 创建注册请求
    final request = RegisterRequest(
      username: _usernameController.text.trim(),
      password: _passwordController.text,
      phoneNumber: _phoneController.text.trim(),
      verificationCode: _codeController.text.trim(),
      memberCode: memberCode,
    );

    final success = await _authService.register(request);
    if (success) {
      // 显示成功消息
      Get.snackbar(
        '注册成功',
        '账号创建成功！请使用新账号登录',
        duration: const Duration(seconds: 3),
      );

      // 延迟一下再跳转，让用户看到成功消息
      await Future.delayed(const Duration(milliseconds: 1000));

      // 明确跳转到登录页面
      Get.offAll(() => const LoginScreen());
    }
  }
}
