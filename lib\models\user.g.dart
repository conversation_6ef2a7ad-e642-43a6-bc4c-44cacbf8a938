// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserAdapter extends TypeAdapter<User> {
  @override
  final int typeId = 20;

  @override
  User read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return User(
      id: fields[0] as String,
      username: fields[1] as String,
      phoneNumber: fields[2] as String,
      email: fields[3] as String?,
      avatar: fields[4] as String?,
      isMember: fields[5] as bool,
      memberExpireTime: fields[6] as DateTime?,
      membershipType: fields[7] as MembershipType,
      createdAt: fields[8] as DateTime,
      updatedAt: fields[9] as DateTime,
      isDataSyncEnabled: fields[10] as bool,
      memberCode: fields[11] as String?,
      isPermanentMember: fields[12] as bool,
      settings: fields[13] as UserSettings,
    );
  }

  @override
  void write(BinaryWriter writer, User obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.username)
      ..writeByte(2)
      ..write(obj.phoneNumber)
      ..writeByte(3)
      ..write(obj.email)
      ..writeByte(4)
      ..write(obj.avatar)
      ..writeByte(5)
      ..write(obj.isMember)
      ..writeByte(6)
      ..write(obj.memberExpireTime)
      ..writeByte(7)
      ..write(obj.membershipType)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.updatedAt)
      ..writeByte(10)
      ..write(obj.isDataSyncEnabled)
      ..writeByte(11)
      ..write(obj.memberCode)
      ..writeByte(12)
      ..write(obj.isPermanentMember)
      ..writeByte(13)
      ..write(obj.settings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserSettingsAdapter extends TypeAdapter<UserSettings> {
  @override
  final int typeId = 22;

  @override
  UserSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserSettings(
      enableBiometric: fields[0] as bool,
      autoSync: fields[1] as bool,
      enableNotification: fields[2] as bool,
      theme: fields[3] as String,
      language: fields[4] as String,
    );
  }

  @override
  void write(BinaryWriter writer, UserSettings obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.enableBiometric)
      ..writeByte(1)
      ..write(obj.autoSync)
      ..writeByte(2)
      ..write(obj.enableNotification)
      ..writeByte(3)
      ..write(obj.theme)
      ..writeByte(4)
      ..write(obj.language);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MembershipTypeAdapter extends TypeAdapter<MembershipType> {
  @override
  final int typeId = 21;

  @override
  MembershipType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return MembershipType.none;
      case 1:
        return MembershipType.monthly;
      case 2:
        return MembershipType.permanent;
      default:
        return MembershipType.none;
    }
  }

  @override
  void write(BinaryWriter writer, MembershipType obj) {
    switch (obj) {
      case MembershipType.none:
        writer.writeByte(0);
        break;
      case MembershipType.monthly:
        writer.writeByte(1);
        break;
      case MembershipType.permanent:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MembershipTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: json['id'] as String,
      username: json['username'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String?,
      avatar: json['avatar'] as String?,
      isMember: json['isMember'] as bool? ?? false,
      memberExpireTime: json['memberExpireTime'] == null
          ? null
          : DateTime.parse(json['memberExpireTime'] as String),
      membershipType: $enumDecodeNullable(
              _$MembershipTypeEnumMap, json['membershipType']) ??
          MembershipType.none,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDataSyncEnabled: json['isDataSyncEnabled'] as bool? ?? true,
      memberCode: json['memberCode'] as String?,
      isPermanentMember: json['isPermanentMember'] as bool? ?? false,
      settings: UserSettings.fromJson(json['settings'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'avatar': instance.avatar,
      'isMember': instance.isMember,
      'memberExpireTime': instance.memberExpireTime?.toIso8601String(),
      'membershipType': _$MembershipTypeEnumMap[instance.membershipType]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDataSyncEnabled': instance.isDataSyncEnabled,
      'memberCode': instance.memberCode,
      'isPermanentMember': instance.isPermanentMember,
      'settings': instance.settings,
    };

const _$MembershipTypeEnumMap = {
  MembershipType.none: 'none',
  MembershipType.monthly: 'monthly',
  MembershipType.permanent: 'permanent',
};

UserSettings _$UserSettingsFromJson(Map<String, dynamic> json) => UserSettings(
      enableBiometric: json['enableBiometric'] as bool? ?? false,
      autoSync: json['autoSync'] as bool? ?? true,
      enableNotification: json['enableNotification'] as bool? ?? true,
      theme: json['theme'] as String? ?? 'system',
      language: json['language'] as String? ?? 'zh-CN',
    );

Map<String, dynamic> _$UserSettingsToJson(UserSettings instance) =>
    <String, dynamic>{
      'enableBiometric': instance.enableBiometric,
      'autoSync': instance.autoSync,
      'enableNotification': instance.enableNotification,
      'theme': instance.theme,
      'language': instance.language,
    };

LoginResponse _$LoginResponseFromJson(Map<String, dynamic> json) =>
    LoginResponse(
      token: json['token'] as String,
      refreshToken: json['refreshToken'] as String,
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      expiresIn: (json['expiresIn'] as num).toInt(),
    );

Map<String, dynamic> _$LoginResponseToJson(LoginResponse instance) =>
    <String, dynamic>{
      'token': instance.token,
      'refreshToken': instance.refreshToken,
      'user': instance.user,
      'expiresIn': instance.expiresIn,
    };

RegisterRequest _$RegisterRequestFromJson(Map<String, dynamic> json) =>
    RegisterRequest(
      username: json['username'] as String,
      password: json['password'] as String,
      phoneNumber: json['phoneNumber'] as String,
      verificationCode: json['verificationCode'] as String,
      memberCode: json['memberCode'] as String?,
    );

Map<String, dynamic> _$RegisterRequestToJson(RegisterRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'password': instance.password,
      'phoneNumber': instance.phoneNumber,
      'verificationCode': instance.verificationCode,
      'memberCode': instance.memberCode,
    };

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
      username: json['username'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'password': instance.password,
    };
