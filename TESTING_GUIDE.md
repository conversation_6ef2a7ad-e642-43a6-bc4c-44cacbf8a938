# 岱宗文脉账号系统测试指南

## 🚀 快速开始测试

### 方案一：本地Mock服务器测试（推荐）

这是最简单快速的测试方案，无需任何云服务配置。

#### 1. 启动Mock服务器

```bash
# 进入mock服务器目录
cd mock-server

# 安装依赖
npm install

# 启动服务器
npm start
```

服务器将在 `http://localhost:3000` 启动

#### 2. 启动Flutter应用

```bash
# 在项目根目录
flutter run -d windows
```

#### 3. 测试功能

##### 测试数据
- **测试用户名**: `testuser`
- **测试密码**: `test123` / `password` / `123456` (已启用密码验证)
- **测试手机号**: `13800138000` (已被使用)
- **新注册手机号**: `13900139001`, `13900139002` 等
- **测试验证码**: `123456` (固定)
- **测试会员码**: `VIP2024001` 或 `TEST123456`

##### 测试流程

1. **打开测试页面**
   - 点击应用左上角菜单
   - 选择"账号系统测试"

2. **测试注册功能**
   - 点击"登录/注册"
   - 选择"立即注册"
   - 输入用户名（如：`newuser001`）
   - 输入手机号（如：`13900139001`）- 注意使用未被占用的手机号
   - 点击"发送验证码"，输入 `123456`
   - 设置密码（如：`test123`）
   - 可选输入会员码：`VIP2024001`（获得永久会员）
   - 点击注册
   - 注册成功后会自动跳转回登录页面

3. **测试登录功能**
   - 使用用户名 `testuser` 和密码 `test123` 登录
   - 或使用刚注册的账号和设置的密码登录
   - 注意：现在已启用密码验证，必须使用正确密码

4. **测试会员功能**
   - 登录后查看会员状态
   - 测试功能权限检查
   - 测试使用限制验证

5. **测试数据同步**
   - 在设置中开启数据同步
   - 点击"手动同步"测试

## 🔧 测试方案对比

### 方案一：本地Mock服务器 ⭐⭐⭐⭐⭐
**优点**：
- ✅ 无需云服务配置
- ✅ 快速启动测试
- ✅ 完全控制测试数据
- ✅ 离线测试
- ✅ 免费

**缺点**：
- ❌ 不是真实环境
- ❌ 无法测试真实支付

**适用场景**：功能验证、开发调试、演示

### 方案二：腾讯云Serverless部署 ⭐⭐⭐⭐
**优点**：
- ✅ 真实云环境
- ✅ 可测试真实支付
- ✅ 生产级别测试
- ✅ 弹性扩展

**缺点**：
- ❌ 需要云服务配置
- ❌ 有一定成本
- ❌ 配置复杂

**适用场景**：生产部署、真实环境测试

### 方案三：Firebase测试 ⭐⭐⭐
**优点**：
- ✅ 快速部署
- ✅ 免费额度
- ✅ 集成度高

**缺点**：
- ❌ 需要Google服务
- ❌ 功能限制
- ❌ 不符合原设计

**适用场景**：快速原型验证

## 📋 详细测试清单

### 用户认证测试
- [ ] 发送短信验证码
- [ ] 验证码验证
- [ ] 用户注册（普通用户）
- [ ] 用户注册（会员码用户）
- [ ] 用户登录
- [ ] Token刷新
- [ ] 退出登录

### 会员系统测试
- [ ] 查看会员套餐
- [ ] 创建订单
- [ ] 会员码支付
- [ ] 会员权限验证
- [ ] 功能限制检查
- [ ] 会员状态显示

### 数据同步测试
- [ ] 数据上传同步
- [ ] 数据下载同步
- [ ] 同步开关控制
- [ ] 冲突处理

### 用户设置测试
- [ ] 修改用户名
- [ ] 修改密码
- [ ] 上传头像
- [ ] 更新设置
- [ ] 生物识别开关

## 🐛 常见问题解决

### 1. Mock服务器启动失败
```bash
# 检查端口是否被占用
netstat -ano | findstr :3000

# 更换端口（修改server.js中的PORT）
const PORT = 3001;
```

### 2. Flutter网络连接失败
- 确保Mock服务器正在运行
- 检查防火墙设置
- 确认API地址配置正确

### 3. 验证码发送失败
- Mock服务器中验证码固定为 `123456`
- 检查控制台日志确认请求到达

### 4. 会员码无效
- 使用预设的测试会员码：`VIP2024001` 或 `TEST123456`
- 检查会员码是否已被使用

## 🚀 进阶测试

### 1. 压力测试
```bash
# 使用Apache Bench测试
ab -n 100 -c 10 http://localhost:3000/packages

# 使用curl测试登录
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"test123"}'
```

### 2. 自动化测试
可以编写Flutter集成测试：

```dart
// test/integration_test/account_system_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:novel_app/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('账号系统测试', () {
    testWidgets('用户注册流程', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // 测试注册流程
      // ...
    });
  });
}
```

## 📊 测试报告模板

### 测试环境
- **Flutter版本**: 3.x.x
- **测试平台**: Windows/Android/iOS
- **Mock服务器**: localhost:3000
- **测试时间**: YYYY-MM-DD

### 测试结果
| 功能模块 | 测试用例 | 状态 | 备注 |
|---------|---------|------|------|
| 用户注册 | 普通注册 | ✅ | 正常 |
| 用户注册 | 会员码注册 | ✅ | 正常 |
| 用户登录 | 密码登录 | ✅ | 正常 |
| 会员系统 | 套餐显示 | ✅ | 正常 |
| 会员系统 | 会员码支付 | ✅ | 正常 |
| 数据同步 | 手动同步 | ✅ | 正常 |

### 发现问题
1. 问题描述
2. 重现步骤
3. 预期结果
4. 实际结果
5. 解决方案

## 🎯 下一步计划

### 短期目标（1-2天）
1. ✅ 完成本地Mock测试
2. ⏳ 修复发现的问题
3. ⏳ 优化用户体验

### 中期目标（1周）
1. ⏳ 部署腾讯云Serverless
2. ⏳ 配置真实支付
3. ⏳ 生产环境测试

### 长期目标（1个月）
1. ⏳ 用户反馈收集
2. ⏳ 功能优化迭代
3. ⏳ 性能监控

---

**开始测试**: 按照上述步骤，您现在就可以开始测试账号系统的所有功能了！
