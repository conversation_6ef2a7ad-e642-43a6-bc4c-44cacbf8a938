# 腾讯云Serverless账号系统后端架构指南

## 1. 架构概述

本账号系统基于腾讯云Serverless架构，包含以下核心组件：

- **云函数 (SCF)**: 处理业务逻辑
- **API网关**: 提供RESTful API接口
- **云数据库 MySQL**: 存储用户数据
- **对象存储 COS**: 存储用户头像等文件
- **短信服务 SMS**: 发送验证码
- **微信支付**: 处理支付逻辑

## 2. 数据库设计

### 2.1 用户表 (users)
```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(100),
    avatar_url VARCHAR(500),
    is_member BOOLEAN DEFAULT FALSE,
    member_expire_time DATETIME,
    membership_type ENUM('none', 'monthly', 'permanent') DEFAULT 'none',
    is_permanent_member BOOLEA<PERSON> DEFAULT FALSE,
    member_code VARCHAR(50),
    is_data_sync_enabled BOOLEAN DEFAULT TRUE,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.2 会员套餐表 (membership_packages)
```sql
CREATE TABLE membership_packages (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration_days INT NOT NULL COMMENT '-1表示永久',
    features JSON,
    limits JSON,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.3 订单表 (orders)
```sql
CREATE TABLE orders (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    package_id VARCHAR(36) NOT NULL,
    package_name VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'paid', 'cancelled', 'refunded', 'expired') DEFAULT 'pending',
    payment_method ENUM('wechat', 'alipay', 'member_code'),
    transaction_id VARCHAR(100),
    member_code VARCHAR(50),
    expire_at DATETIME,
    paid_at DATETIME,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 2.4 会员码表 (member_codes)
```sql
CREATE TABLE member_codes (
    code VARCHAR(50) PRIMARY KEY,
    package_id VARCHAR(36) NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    used_by VARCHAR(36),
    used_at DATETIME,
    expire_at DATETIME,
    batch_id VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (used_by) REFERENCES users(id)
);
```

### 2.5 用户数据同步表 (user_sync_data)
```sql
CREATE TABLE user_sync_data (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    data_type ENUM('novels', 'characters', 'knowledge', 'settings') NOT NULL,
    data JSON NOT NULL,
    version INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY unique_user_data_type (user_id, data_type)
);
```

## 3. 云函数设计

### 3.1 认证相关函数

#### auth-send-code
```python
import json
import random
import string
from tencentcloud.sms.v20210111 import sms_client, models

def main_handler(event, context):
    """发送短信验证码"""
    try:
        body = json.loads(event['body'])
        phone_number = body['phoneNumber']
        
        # 生成6位验证码
        code = ''.join(random.choices(string.digits, k=6))
        
        # 存储验证码到Redis（5分钟过期）
        redis_client.setex(f"sms_code:{phone_number}", 300, code)
        
        # 发送短信
        send_sms(phone_number, code)
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'message': '验证码发送成功'
            })
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'message': str(e)
            })
        }
```

#### auth-register
```python
import json
import uuid
import hashlib
from datetime import datetime

def main_handler(event, context):
    """用户注册"""
    try:
        body = json.loads(event['body'])
        
        # 验证验证码
        if not verify_sms_code(body['phoneNumber'], body['verificationCode']):
            return error_response('验证码错误或已过期')
        
        # 检查用户名和手机号是否已存在
        if user_exists(body['username'], body['phoneNumber']):
            return error_response('用户名或手机号已存在')
        
        # 验证会员码（如果提供）
        member_info = None
        if body.get('memberCode'):
            member_info = validate_member_code(body['memberCode'])
            if not member_info:
                return error_response('会员码无效')
        
        # 创建用户
        user_id = str(uuid.uuid4())
        password_hash = hashlib.sha256(body['password'].encode()).hexdigest()
        
        user_data = {
            'id': user_id,
            'username': body['username'],
            'password_hash': password_hash,
            'phone_number': body['phoneNumber'],
            'is_member': bool(member_info),
            'is_permanent_member': bool(member_info),
            'member_code': body.get('memberCode'),
            'settings': {
                'enableBiometric': False,
                'autoSync': True,
                'enableNotification': True,
                'theme': 'system',
                'language': 'zh-CN'
            }
        }
        
        # 保存用户到数据库
        create_user(user_data)
        
        # 如果使用了会员码，标记为已使用
        if member_info:
            mark_member_code_used(body['memberCode'], user_id)
        
        # 生成JWT Token
        token_data = generate_tokens(user_id)
        
        return success_response({
            'token': token_data['token'],
            'refreshToken': token_data['refreshToken'],
            'user': get_user_info(user_id),
            'expiresIn': 86400
        })
        
    except Exception as e:
        return error_response(str(e))
```

### 3.2 支付相关函数

#### payment-wechat
```python
import json
from wechatpay import WeChatPay

def main_handler(event, context):
    """微信支付"""
    try:
        body = json.loads(event['body'])
        order_id = body['orderId']
        
        # 获取订单信息
        order = get_order(order_id)
        if not order or not order['canPay']:
            return error_response('订单不存在或无法支付')
        
        # 调用微信支付API
        wxpay = WeChatPay()
        payment_data = wxpay.create_order(
            out_trade_no=order_id,
            description=order['packageName'],
            amount=int(order['amount'] * 100),  # 转换为分
            notify_url='https://your-domain.com/payment/notify'
        )
        
        return success_response(payment_data)
        
    except Exception as e:
        return error_response(str(e))
```

### 3.3 数据同步函数

#### sync-upload
```python
import json
from datetime import datetime

def main_handler(event, context):
    """上传用户数据"""
    try:
        # 验证用户身份
        user_id = verify_jwt_token(event['headers']['Authorization'])
        if not user_id:
            return error_response('未授权', 401)
        
        body = json.loads(event['body'])
        sync_data = body['data']
        
        # 保存各类数据
        for data_type, data_content in sync_data.items():
            save_sync_data(user_id, data_type, data_content)
        
        return success_response({'message': '数据同步成功'})
        
    except Exception as e:
        return error_response(str(e))
```

## 4. API网关配置

### 4.1 路由配置
```yaml
# serverless.yml
service: novel-app-backend

provider:
  name: tencentcloud
  runtime: Python3.6
  region: ap-guangzhou

functions:
  # 认证相关
  auth-send-code:
    handler: auth/send_code.main_handler
    events:
      - apigw:
          path: /auth/send-code
          method: POST
  
  auth-register:
    handler: auth/register.main_handler
    events:
      - apigw:
          path: /auth/register
          method: POST
  
  auth-login:
    handler: auth/login.main_handler
    events:
      - apigw:
          path: /auth/login
          method: POST
  
  # 支付相关
  payment-wechat:
    handler: payment/wechat.main_handler
    events:
      - apigw:
          path: /payment/wechat
          method: POST
  
  # 数据同步
  sync-upload:
    handler: sync/upload.main_handler
    events:
      - apigw:
          path: /sync/upload
          method: POST

resources:
  Resources:
    # MySQL数据库
    NovelAppDB:
      Type: TencentCloud::Cdb::DBInstance
      Properties:
        EngineVersion: "5.7"
        Memory: 1000
        Volume: 25
        
    # Redis缓存
    NovelAppRedis:
      Type: TencentCloud::Redis::Instance
      Properties:
        TypeId: 2
        MemSize: 1024
```

## 5. 部署步骤

### 5.1 环境准备
```bash
# 安装Serverless Framework
npm install -g serverless

# 安装腾讯云插件
npm install -g serverless-tencent-scf

# 配置腾讯云密钥
serverless config credentials --provider tencentcloud --key <your-key> --secret <your-secret>
```

### 5.2 部署命令
```bash
# 部署所有函数
serverless deploy

# 部署单个函数
serverless deploy function -f auth-register

# 查看日志
serverless logs -f auth-register
```

## 6. 安全配置

### 6.1 JWT Token配置
```python
import jwt
from datetime import datetime, timedelta

SECRET_KEY = "your-secret-key"

def generate_tokens(user_id):
    """生成访问令牌和刷新令牌"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(days=1)
    }
    
    token = jwt.encode(payload, SECRET_KEY, algorithm='HS256')
    
    refresh_payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(days=30)
    }
    
    refresh_token = jwt.encode(refresh_payload, SECRET_KEY, algorithm='HS256')
    
    return {
        'token': token,
        'refreshToken': refresh_token
    }
```

### 6.2 API限流配置
```python
import redis
from datetime import datetime, timedelta

def rate_limit(key, limit=100, window=3600):
    """API限流装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            redis_client = redis.Redis()
            current_time = datetime.now()
            window_start = current_time - timedelta(seconds=window)
            
            # 清理过期记录
            redis_client.zremrangebyscore(key, 0, window_start.timestamp())
            
            # 检查当前请求数
            current_requests = redis_client.zcard(key)
            if current_requests >= limit:
                return error_response('请求过于频繁', 429)
            
            # 记录当前请求
            redis_client.zadd(key, {str(current_time.timestamp()): current_time.timestamp()})
            redis_client.expire(key, window)
            
            return func(*args, **kwargs)
        return wrapper
    return decorator
```

## 7. 监控和日志

### 7.1 云监控配置
- 配置函数执行时间监控
- 设置错误率告警
- 监控数据库连接数

### 7.2 日志收集
```python
import logging
import json

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def log_request(event, context, response):
    """记录请求日志"""
    log_data = {
        'requestId': context.request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path'),
        'statusCode': response.get('statusCode'),
        'timestamp': datetime.now().isoformat()
    }
    
    logging.info(json.dumps(log_data))
```

这个架构提供了完整的账号系统后端支持，包括用户认证、支付处理、数据同步等核心功能。通过腾讯云Serverless架构，可以实现高可用、弹性扩展的服务。
