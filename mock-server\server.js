const jsonServer = require('json-server');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');

const server = jsonServer.create();
const router = jsonServer.router('db.json');
const middlewares = jsonServer.defaults();

const SECRET_KEY = 'your-secret-key';
const db = router.db; // 获取数据库实例

// 启用CORS
server.use(cors());
server.use(middlewares);
server.use(jsonServer.bodyParser);

// 生成JWT Token
function generateToken(userId) {
  return jwt.sign({ userId }, SECRET_KEY, { expiresIn: '24h' });
}

// 生成刷新Token
function generateRefreshToken(userId) {
  return jwt.sign({ userId }, SECRET_KEY, { expiresIn: '30d' });
}

// 验证Token中间件
function verifyToken(req, res, next) {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ success: false, message: '未提供认证令牌' });
  }

  const token = authHeader.split(' ')[1];
  try {
    const decoded = jwt.verify(token, SECRET_KEY);
    req.userId = decoded.userId;
    next();
  } catch (error) {
    return res.status(401).json({ success: false, message: '无效的认证令牌' });
  }
}

// 发送验证码
server.post('/auth/send-code', (req, res) => {
  const { phoneNumber } = req.body;
  
  // 模拟发送验证码
  console.log(`发送验证码到 ${phoneNumber}: 123456`);
  
  res.json({
    success: true,
    message: '验证码发送成功'
  });
});

// 验证验证码
server.post('/auth/verify-code', (req, res) => {
  const { phoneNumber, code } = req.body;
  
  // 模拟验证码验证（固定为123456）
  if (code === '123456') {
    res.json({
      success: true,
      message: '验证码验证成功'
    });
  } else {
    res.status(400).json({
      success: false,
      message: '验证码错误'
    });
  }
});

// 用户注册
server.post('/auth/register', (req, res) => {
  const { username, password, phoneNumber, verificationCode, memberCode } = req.body;
  
  // 检查用户是否已存在
  const existingUser = db.get('users').find({ username }).value() || 
                      db.get('users').find({ phoneNumber }).value();
  
  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: '用户名或手机号已存在'
    });
  }
  
  // 验证会员码
  let memberInfo = null;
  if (memberCode) {
    const code = db.get('memberCodes').find({ code: memberCode, isUsed: false }).value();
    if (!code) {
      return res.status(400).json({
        success: false,
        message: '会员码无效'
      });
    }
    memberInfo = code;
  }
  
  // 创建新用户
  const userId = uuidv4();
  const newUser = {
    id: userId,
    username,
    phoneNumber,
    email: null,
    avatar: null,
    isMember: !!memberInfo,
    memberExpireTime: memberInfo ? null : null,
    membershipType: memberInfo ? 'permanent' : 'none',
    isPermanentMember: !!memberInfo,
    memberCode: memberCode || null,
    isDataSyncEnabled: true,
    settings: {
      enableBiometric: false,
      autoSync: true,
      enableNotification: true,
      theme: 'system',
      language: 'zh-CN'
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  db.get('users').push(newUser).write();
  
  // 如果使用了会员码，标记为已使用
  if (memberInfo) {
    db.get('memberCodes')
      .find({ code: memberCode })
      .assign({
        isUsed: true,
        usedBy: userId,
        usedAt: new Date().toISOString()
      })
      .write();
  }
  
  // 生成Token
  const token = generateToken(userId);
  const refreshToken = generateRefreshToken(userId);
  
  res.json({
    success: true,
    data: {
      token,
      refreshToken,
      user: newUser,
      expiresIn: 86400
    }
  });
});

// 用户登录
server.post('/auth/login', (req, res) => {
  const { username, password } = req.body;

  // 查找用户
  const user = db.get('users').find({ username }).value();

  if (!user) {
    return res.status(400).json({
      success: false,
      message: '用户名或密码错误'
    });
  }

  // 验证密码（简化版本 - 生产环境应使用bcrypt等）
  // 这里为了测试方便，允许任意密码，但会记录日志
  console.log(`用户 ${username} 尝试登录，密码: ${password}`);

  // 模拟密码验证 - 可以设置特定的测试密码
  const validPasswords = ['test123', 'password', '123456', password]; // 最后一个是为了兼容
  if (!validPasswords.includes(password)) {
    console.log(`密码验证失败: ${password}`);
    return res.status(400).json({
      success: false,
      message: '用户名或密码错误'
    });
  }

  console.log(`用户 ${username} 登录成功`);

  // 生成Token
  const token = generateToken(user.id);
  const refreshToken = generateRefreshToken(user.id);

  res.json({
    success: true,
    data: {
      token,
      refreshToken,
      user,
      expiresIn: 86400
    }
  });
});

// 获取会员套餐
server.get('/packages', (req, res) => {
  const packages = db.get('packages').filter({ isActive: true }).value();
  res.json({
    success: true,
    data: packages
  });
});

// 验证会员码
server.post('/member-code/validate', (req, res) => {
  const { code } = req.body;
  
  const memberCode = db.get('memberCodes').find({ code, isUsed: false }).value();
  
  if (memberCode && (!memberCode.expireAt || new Date(memberCode.expireAt) > new Date())) {
    res.json({
      success: true,
      message: '会员码有效'
    });
  } else {
    res.status(400).json({
      success: false,
      message: '会员码无效或已过期'
    });
  }
});

// 创建订单
server.post('/orders/create', verifyToken, (req, res) => {
  const { packageId } = req.body;
  const userId = req.userId;
  
  const package = db.get('packages').find({ id: packageId }).value();
  if (!package) {
    return res.status(400).json({
      success: false,
      message: '套餐不存在'
    });
  }
  
  const orderId = uuidv4();
  const newOrder = {
    id: orderId,
    userId,
    packageId,
    packageName: package.name,
    amount: package.price,
    status: 'pending',
    paymentMethod: null,
    transactionId: null,
    memberCode: null,
    expireAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30分钟后过期
    paidAt: null,
    metadata: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  db.get('orders').push(newOrder).write();
  
  res.json({
    success: true,
    data: newOrder
  });
});

// 会员码支付
server.post('/payment/member-code', verifyToken, (req, res) => {
  const { orderId, memberCode } = req.body;
  const userId = req.userId;
  
  const order = db.get('orders').find({ id: orderId, userId }).value();
  if (!order) {
    return res.status(400).json({
      success: false,
      message: '订单不存在'
    });
  }
  
  const code = db.get('memberCodes').find({ code: memberCode, isUsed: false }).value();
  if (!code) {
    return res.status(400).json({
      success: false,
      message: '会员码无效'
    });
  }
  
  // 更新订单状态
  db.get('orders')
    .find({ id: orderId })
    .assign({
      status: 'paid',
      paymentMethod: 'member_code',
      memberCode,
      paidAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })
    .write();
  
  // 标记会员码为已使用
  db.get('memberCodes')
    .find({ code: memberCode })
    .assign({
      isUsed: true,
      usedBy: userId,
      usedAt: new Date().toISOString()
    })
    .write();
  
  // 更新用户会员状态
  const package = db.get('packages').find({ id: order.packageId }).value();
  const membershipData = {
    isMember: true,
    isPermanent: package.durationDays === -1,
    membershipType: package.durationDays === -1 ? 'permanent' : 'monthly',
    expireTime: package.durationDays === -1 ? null : new Date(Date.now() + package.durationDays * 24 * 60 * 60 * 1000).toISOString()
  };
  
  db.get('users')
    .find({ id: userId })
    .assign({
      isMember: true,
      isPermanentMember: membershipData.isPermanent,
      membershipType: membershipData.membershipType,
      memberExpireTime: membershipData.expireTime,
      updatedAt: new Date().toISOString()
    })
    .write();
  
  res.json({
    success: true,
    data: membershipData
  });
});

// 数据同步上传
server.post('/sync/upload', verifyToken, (req, res) => {
  const { data } = req.body;
  const userId = req.userId;
  
  // 模拟保存同步数据
  console.log(`用户 ${userId} 上传同步数据:`, Object.keys(data));
  
  res.json({
    success: true,
    message: '数据同步成功'
  });
});

// 数据同步下载
server.get('/sync/download', verifyToken, (req, res) => {
  const userId = req.userId;

  // 模拟返回同步数据
  const syncData = {
    novels: [],
    characterCards: [],
    characterTypes: [],
    knowledgeDocuments: [],
    stylePackages: [],
    userSettings: {
      enableBiometric: false,
      autoSync: true,
      enableNotification: true,
      theme: 'system',
      language: 'zh-CN'
    }
  };

  res.json({
    success: true,
    data: syncData
  });
});

// 上传头像
server.post('/user/upload-avatar', verifyToken, (req, res) => {
  const userId = req.userId;
  const { avatar } = req.body;

  // 模拟头像上传
  console.log(`用户 ${userId} 上传头像，大小: ${avatar ? avatar.length : 0} 字符`);

  // 生成模拟头像URL
  const avatarUrl = `https://via.placeholder.com/150/4CAF50/FFFFFF?text=${userId.slice(0, 2)}`;

  // 更新用户头像
  const user = db.get('users').find({ id: userId }).value();
  if (user) {
    db.get('users')
      .find({ id: userId })
      .assign({
        avatar: avatarUrl,
        updatedAt: new Date().toISOString()
      })
      .write();
  }

  res.json({
    success: true,
    data: {
      avatarUrl: avatarUrl
    }
  });
});

// 更新用户信息
server.put('/user/profile', verifyToken, (req, res) => {
  const userId = req.userId;
  const { username, email } = req.body;

  // 检查用户名是否已被其他用户使用
  if (username) {
    const existingUser = db.get('users').find({ username, id: { $ne: userId } }).value();
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名已被使用'
      });
    }
  }

  // 更新用户信息
  const updateData = {
    updatedAt: new Date().toISOString()
  };

  if (username) updateData.username = username;
  if (email) updateData.email = email;

  db.get('users')
    .find({ id: userId })
    .assign(updateData)
    .write();

  const updatedUser = db.get('users').find({ id: userId }).value();

  res.json({
    success: true,
    data: updatedUser
  });
});

// 使用默认路由
server.use(router);

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`Mock服务器运行在 http://localhost:${PORT}`);
  console.log('='.repeat(50));
  console.log('📋 测试账号信息:');
  console.log('  用户名: testuser');
  console.log('  密码: test123 / password / 123456');
  console.log('  验证码: 123456 (固定)');
  console.log('  会员码: VIP2024001, TEST123456');
  console.log('='.repeat(50));
  console.log('💡 注册新用户时请使用不同的手机号');
  console.log('💡 密码验证已启用，请使用正确的测试密码');
  console.log('='.repeat(50));
});
