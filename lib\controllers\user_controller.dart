import 'dart:io';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../models/user.dart';
import '../services/auth_service.dart';
import '../services/user_sync_service.dart';
import '../services/payment_service.dart';

/// 用户控制器
class UserController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final UserSyncService _syncService = Get.find<UserSyncService>();
  final PaymentService _paymentService = Get.find<PaymentService>();
  final ImagePicker _imagePicker = ImagePicker();

  // 响应式变量
  Rx<User?> get currentUser => _authService.currentUser;
  RxBool get isLoggedIn => _authService.isLoggedIn;
  RxBool get isSyncEnabled => _syncService.isSyncEnabled;
  RxBool get isSyncing => _syncService.isSyncing;
  Rx<DateTime?> get lastSyncTime => _syncService.lastSyncTime;

  final RxBool isUpdatingProfile = false.obs;
  final RxString avatarPath = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // 监听用户变化
    ever(currentUser, (User? user) {
      if (user != null) {
        avatarPath.value = user.avatar ?? '';
      }
    });
  }

  /// 更新用户名
  Future<bool> updateUsername(String newUsername) async {
    if (newUsername.trim().isEmpty) {
      Get.snackbar('错误', '用户名不能为空');
      return false;
    }

    if (newUsername.length < 2 || newUsername.length > 20) {
      Get.snackbar('错误', '用户名长度应在2-20个字符之间');
      return false;
    }

    try {
      isUpdatingProfile.value = true;
      
      final response = await _authService._dio.put(
        '${ApiConfig.baseUrl}/user/profile',
        data: {'username': newUsername},
      );

      if (response.data['success'] == true) {
        currentUser.value?.username = newUsername;
        currentUser.refresh();
        
        // 如果启用同步，自动同步数据
        if (isSyncEnabled.value) {
          _syncService.syncUserData();
        }
        
        Get.snackbar('成功', '用户名更新成功');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '更新失败');
        return false;
      }
    } catch (e) {
      print('更新用户名失败: $e');
      Get.snackbar('错误', '更新用户名失败: ${e.toString()}');
      return false;
    } finally {
      isUpdatingProfile.value = false;
    }
  }

  /// 更新密码
  Future<bool> updatePassword(String oldPassword, String newPassword) async {
    if (oldPassword.trim().isEmpty || newPassword.trim().isEmpty) {
      Get.snackbar('错误', '密码不能为空');
      return false;
    }

    if (newPassword.length < 6) {
      Get.snackbar('错误', '新密码长度不能少于6位');
      return false;
    }

    try {
      isUpdatingProfile.value = true;
      
      final response = await _authService._dio.put(
        '${ApiConfig.baseUrl}/user/password',
        data: {
          'oldPassword': _authService._hashPassword(oldPassword),
          'newPassword': _authService._hashPassword(newPassword),
        },
      );

      if (response.data['success'] == true) {
        Get.snackbar('成功', '密码更新成功');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '密码更新失败');
        return false;
      }
    } catch (e) {
      print('更新密码失败: $e');
      Get.snackbar('错误', '更新密码失败: ${e.toString()}');
      return false;
    } finally {
      isUpdatingProfile.value = false;
    }
  }

  /// 选择并上传头像
  Future<void> selectAndUploadAvatar() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        await uploadAvatar(File(image.path));
      }
    } catch (e) {
      print('选择头像失败: $e');
      Get.snackbar('错误', '选择头像失败');
    }
  }

  /// 上传头像
  Future<bool> uploadAvatar(File imageFile) async {
    try {
      isUpdatingProfile.value = true;
      
      final formData = FormData.fromMap({
        'avatar': await MultipartFile.fromFile(
          imageFile.path,
          filename: 'avatar.jpg',
        ),
      });

      final response = await _authService._dio.post(
        '${ApiConfig.baseUrl}/user/avatar',
        data: formData,
      );

      if (response.data['success'] == true) {
        final avatarUrl = response.data['data']['avatarUrl'];
        currentUser.value?.avatar = avatarUrl;
        avatarPath.value = avatarUrl;
        currentUser.refresh();
        
        // 如果启用同步，自动同步数据
        if (isSyncEnabled.value) {
          _syncService.syncUserData();
        }
        
        Get.snackbar('成功', '头像更新成功');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '头像上传失败');
        return false;
      }
    } catch (e) {
      print('上传头像失败: $e');
      Get.snackbar('错误', '上传头像失败: ${e.toString()}');
      return false;
    } finally {
      isUpdatingProfile.value = false;
    }
  }

  /// 更新用户设置
  Future<bool> updateSettings(UserSettings settings) async {
    try {
      isUpdatingProfile.value = true;
      
      final response = await _authService._dio.put(
        '${ApiConfig.baseUrl}/user/settings',
        data: settings.toJson(),
      );

      if (response.data['success'] == true) {
        currentUser.value?.settings = settings;
        currentUser.refresh();
        
        // 如果启用同步，自动同步数据
        if (isSyncEnabled.value) {
          _syncService.syncUserData();
        }
        
        Get.snackbar('成功', '设置更新成功');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '设置更新失败');
        return false;
      }
    } catch (e) {
      print('更新设置失败: $e');
      Get.snackbar('错误', '更新设置失败: ${e.toString()}');
      return false;
    } finally {
      isUpdatingProfile.value = false;
    }
  }

  /// 切换数据同步开关
  Future<void> toggleDataSync(bool enabled) async {
    await _syncService.setSyncEnabled(enabled);
  }

  /// 手动同步数据
  Future<void> manualSync() async {
    await _syncService.syncUserData();
  }

  /// 导出用户数据
  Future<void> exportUserData() async {
    try {
      final data = await _syncService.exportUserData();
      
      // 这里可以实现数据导出功能，比如保存到文件或分享
      Get.snackbar('成功', '数据导出完成');
    } catch (e) {
      print('导出数据失败: $e');
      Get.snackbar('错误', '导出数据失败');
    }
  }

  /// 获取会员状态信息
  String getMembershipStatusText() {
    final user = currentUser.value;
    if (user == null) return '未登录';
    
    if (user.isPermanentMember) {
      return '永久会员';
    } else if (user.isValidMember) {
      final remainingDays = user.memberRemainingDays;
      return '会员 (剩余${remainingDays}天)';
    } else {
      return '免费用户';
    }
  }

  /// 获取会员权益描述
  List<String> getMembershipBenefits() {
    final user = currentUser.value;
    if (user == null || !user.isValidMember) {
      return [
        '每部小说最多10章',
        '最多3个知识库文档',
        '每天最多生成3部小说',
        '每次最多生成2000字',
        '基础导出格式',
        '标准AI模型',
      ];
    }
    
    return [
      '无章节数量限制',
      '最多20个知识库文档',
      '无每日生成限制',
      '无字数限制',
      '多种导出格式',
      '高级AI模型',
      '扩展功能',
    ];
  }

  /// 检查功能权限
  bool canUseFeature(String feature) {
    return _paymentService.canUseFeature(feature);
  }

  /// 检查使用限制
  bool isWithinLimits(String limitType, int currentCount) {
    return _paymentService.isWithinLimits(limitType, currentCount);
  }

  /// 注销账号
  Future<void> deleteAccount() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('确认注销'),
        content: const Text('注销账号将删除所有数据，此操作不可恢复。确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('确认注销'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final response = await _authService._dio.delete('${ApiConfig.baseUrl}/user/account');
        
        if (response.data['success'] == true) {
          await _authService.logout();
          Get.snackbar('成功', '账号已注销');
        } else {
          Get.snackbar('错误', response.data['message'] ?? '注销失败');
        }
      } catch (e) {
        print('注销账号失败: $e');
        Get.snackbar('错误', '注销账号失败: ${e.toString()}');
      }
    }
  }
}
