import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/novel.dart';
import '../models/character_card.dart';
import '../models/character_type.dart';
import '../models/knowledge_document.dart';
import '../models/writing_style_package.dart';
import '../config/api_config.dart';
import 'auth_service.dart';

/// 用户数据同步服务
class UserSyncService extends GetxService {
  static const String _syncEnabledKey = 'sync_enabled';
  static const String _lastSyncTimeKey = 'last_sync_time';

  final Dio _dio = Dio();
  final AuthService _authService = Get.find<AuthService>();
  
  final RxBool isSyncEnabled = true.obs;
  final RxBool isSyncing = false.obs;
  final Rx<DateTime?> lastSyncTime = Rx<DateTime?>(null);

  SharedPreferences? _prefs;

  @override
  Future<void> onInit() async {
    super.onInit();
    _prefs = await SharedPreferences.getInstance();
    _loadSyncSettings();
    
    // 监听用户登录状态变化
    ever(_authService.isLoggedIn, (isLoggedIn) {
      if (isLoggedIn && isSyncEnabled.value) {
        // 用户登录后自动同步
        syncUserData();
      }
    });
  }

  /// 加载同步设置
  void _loadSyncSettings() {
    isSyncEnabled.value = _prefs?.getBool(_syncEnabledKey) ?? true;
    final lastSyncStr = _prefs?.getString(_lastSyncTimeKey);
    if (lastSyncStr != null) {
      lastSyncTime.value = DateTime.parse(lastSyncStr);
    }
  }

  /// 设置同步开关
  Future<void> setSyncEnabled(bool enabled) async {
    isSyncEnabled.value = enabled;
    await _prefs?.setBool(_syncEnabledKey, enabled);
    
    if (enabled && _authService.isLoggedIn.value) {
      // 启用同步时立即同步一次
      await syncUserData();
    }
  }

  /// 同步用户数据
  Future<bool> syncUserData() async {
    if (!_authService.isLoggedIn.value || !isSyncEnabled.value) {
      return false;
    }

    try {
      isSyncing.value = true;
      
      // 获取本地数据
      final localData = await _collectLocalData();
      
      // 上传到服务器
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/sync/upload',
        data: {
          'data': localData,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (response.data['success'] == true) {
        // 下载服务器数据
        await _downloadServerData();
        
        // 更新最后同步时间
        lastSyncTime.value = DateTime.now();
        await _prefs?.setString(_lastSyncTimeKey, lastSyncTime.value!.toIso8601String());
        
        Get.snackbar('成功', '数据同步完成');
        return true;
      } else {
        Get.snackbar('错误', '数据同步失败: ${response.data['message']}');
        return false;
      }
    } catch (e) {
      print('数据同步失败: $e');
      Get.snackbar('错误', '数据同步失败: ${e.toString()}');
      return false;
    } finally {
      isSyncing.value = false;
    }
  }

  /// 收集本地数据
  Future<Map<String, dynamic>> _collectLocalData() async {
    final data = <String, dynamic>{};
    
    try {
      // 收集小说数据
      final novelController = Get.find<NovelController>();
      data['novels'] = novelController.novels.map((novel) => novel.toJson()).toList();
      
      // 收集角色卡片数据
      final characterController = Get.find<CharacterCardController>();
      data['characterCards'] = characterController.characterCards.map((card) => card.toJson()).toList();
      
      // 收集自定义角色类型
      final characterTypeController = Get.find<CharacterTypeController>();
      data['characterTypes'] = characterTypeController.characterTypes.map((type) => type.toJson()).toList();
      
      // 收集知识库文档
      final knowledgeController = Get.find<KnowledgeBaseController>();
      data['knowledgeDocuments'] = knowledgeController.documents.map((doc) => doc.toJson()).toList();
      
      // 收集写作风格包
      final styleController = Get.find<WritingStylePackageController>();
      data['stylePackages'] = styleController.packages.map((pkg) => pkg.toJson()).toList();
      
      // 收集用户设置
      data['userSettings'] = _authService.currentUser.value?.settings.toJson();
      
    } catch (e) {
      print('收集本地数据失败: $e');
    }
    
    return data;
  }

  /// 下载服务器数据
  Future<void> _downloadServerData() async {
    try {
      final response = await _dio.get('${ApiConfig.baseUrl}/sync/download');
      
      if (response.data['success'] == true) {
        final serverData = response.data['data'];
        await _applyServerData(serverData);
      }
    } catch (e) {
      print('下载服务器数据失败: $e');
    }
  }

  /// 应用服务器数据
  Future<void> _applyServerData(Map<String, dynamic> serverData) async {
    try {
      // 应用小说数据
      if (serverData['novels'] != null) {
        final novelController = Get.find<NovelController>();
        final serverNovels = (serverData['novels'] as List)
            .map((json) => Novel.fromJson(json))
            .toList();
        await _mergeNovels(novelController, serverNovels);
      }
      
      // 应用角色卡片数据
      if (serverData['characterCards'] != null) {
        final characterController = Get.find<CharacterCardController>();
        final serverCards = (serverData['characterCards'] as List)
            .map((json) => CharacterCard.fromJson(json))
            .toList();
        await _mergeCharacterCards(characterController, serverCards);
      }
      
      // 应用自定义角色类型
      if (serverData['characterTypes'] != null) {
        final characterTypeController = Get.find<CharacterTypeController>();
        final serverTypes = (serverData['characterTypes'] as List)
            .map((json) => CharacterType.fromJson(json))
            .toList();
        await _mergeCharacterTypes(characterTypeController, serverTypes);
      }
      
      // 应用知识库文档
      if (serverData['knowledgeDocuments'] != null) {
        final knowledgeController = Get.find<KnowledgeBaseController>();
        final serverDocs = (serverData['knowledgeDocuments'] as List)
            .map((json) => KnowledgeDocument.fromJson(json))
            .toList();
        await _mergeKnowledgeDocuments(knowledgeController, serverDocs);
      }
      
      // 应用写作风格包
      if (serverData['stylePackages'] != null) {
        final styleController = Get.find<WritingStylePackageController>();
        final serverPackages = (serverData['stylePackages'] as List)
            .map((json) => WritingStylePackage.fromJson(json))
            .toList();
        await _mergeStylePackages(styleController, serverPackages);
      }
      
      // 应用用户设置
      if (serverData['userSettings'] != null) {
        final settings = UserSettings.fromJson(serverData['userSettings']);
        if (_authService.currentUser.value != null) {
          _authService.currentUser.value!.settings = settings;
          // 保存到本地
          await _prefs?.setString('user_data', jsonEncode(_authService.currentUser.value!.toJson()));
        }
      }
      
    } catch (e) {
      print('应用服务器数据失败: $e');
    }
  }

  /// 合并小说数据
  Future<void> _mergeNovels(dynamic controller, List<Novel> serverNovels) async {
    // 实现数据合并逻辑，以最新更新时间为准
    for (final serverNovel in serverNovels) {
      final localIndex = controller.novels.indexWhere((n) => n.id == serverNovel.id);
      if (localIndex == -1) {
        // 本地没有，直接添加
        controller.novels.add(serverNovel);
      } else {
        // 本地有，比较更新时间
        final localNovel = controller.novels[localIndex];
        if (serverNovel.updatedAt != null && 
            (localNovel.updatedAt == null || serverNovel.updatedAt!.isAfter(localNovel.updatedAt!))) {
          controller.novels[localIndex] = serverNovel;
        }
      }
    }
    await controller.saveNovels();
  }

  /// 合并角色卡片数据
  Future<void> _mergeCharacterCards(dynamic controller, List<CharacterCard> serverCards) async {
    // 类似的合并逻辑
    for (final serverCard in serverCards) {
      final localIndex = controller.characterCards.indexWhere((c) => c.id == serverCard.id);
      if (localIndex == -1) {
        controller.characterCards.add(serverCard);
      } else {
        // 可以根据需要实现更复杂的合并逻辑
        controller.characterCards[localIndex] = serverCard;
      }
    }
    await controller.saveCharacterCards();
  }

  /// 合并角色类型数据
  Future<void> _mergeCharacterTypes(dynamic controller, List<CharacterType> serverTypes) async {
    for (final serverType in serverTypes) {
      final localIndex = controller.characterTypes.indexWhere((t) => t.id == serverType.id);
      if (localIndex == -1) {
        controller.characterTypes.add(serverType);
      } else {
        controller.characterTypes[localIndex] = serverType;
      }
    }
    await controller.saveCharacterTypes();
  }

  /// 合并知识库文档数据
  Future<void> _mergeKnowledgeDocuments(dynamic controller, List<KnowledgeDocument> serverDocs) async {
    for (final serverDoc in serverDocs) {
      final localIndex = controller.documents.indexWhere((d) => d.id == serverDoc.id);
      if (localIndex == -1) {
        controller.documents.add(serverDoc);
      } else {
        final localDoc = controller.documents[localIndex];
        if (serverDoc.updatedAt.isAfter(localDoc.updatedAt)) {
          controller.documents[localIndex] = serverDoc;
        }
      }
    }
    await controller.saveDocuments();
  }

  /// 合并写作风格包数据
  Future<void> _mergeStylePackages(dynamic controller, List<WritingStylePackage> serverPackages) async {
    for (final serverPackage in serverPackages) {
      final localIndex = controller.packages.indexWhere((p) => p.id == serverPackage.id);
      if (localIndex == -1) {
        controller.packages.add(serverPackage);
      } else {
        controller.packages[localIndex] = serverPackage;
      }
    }
    await controller.savePackages();
  }

  /// 导出用户数据
  Future<Map<String, dynamic>> exportUserData() async {
    return await _collectLocalData();
  }

  /// 导入用户数据
  Future<bool> importUserData(Map<String, dynamic> data) async {
    try {
      await _applyServerData(data);
      Get.snackbar('成功', '数据导入完成');
      return true;
    } catch (e) {
      print('数据导入失败: $e');
      Get.snackbar('错误', '数据导入失败: ${e.toString()}');
      return false;
    }
  }
}
