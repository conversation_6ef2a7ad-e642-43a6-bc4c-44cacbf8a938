import 'dart:convert';
import 'package:dio/dio.dart' as dio_pkg;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/novel.dart';
import '../models/character_card.dart';
import '../models/character_type.dart';
import '../models/knowledge_document.dart';
import '../models/writing_style_package.dart';
import '../config/api_config.dart';
import '../controllers/novel_controller.dart';
import '../controllers/knowledge_base_controller.dart';
import '../controllers/writing_style_package_controller.dart';
import 'auth_service.dart';

/// 用户数据同步服务
class UserSyncService extends GetxService {
  static const String _syncEnabledKey = 'sync_enabled';
  static const String _lastSyncTimeKey = 'last_sync_time';

  final dio_pkg.Dio _dio = dio_pkg.Dio();
  final AuthService _authService = Get.find<AuthService>();
  
  final RxBool isSyncEnabled = true.obs;
  final RxBool isSyncing = false.obs;
  final Rx<DateTime?> lastSyncTime = Rx<DateTime?>(null);

  SharedPreferences? _prefs;

  @override
  Future<void> onInit() async {
    super.onInit();
    _prefs = await SharedPreferences.getInstance();
    _loadSyncSettings();
    
    // 监听用户登录状态变化
    ever(_authService.isLoggedIn, (isLoggedIn) {
      if (isLoggedIn && isSyncEnabled.value) {
        // 用户登录后自动同步
        syncUserData();
      }
    });
  }

  /// 加载同步设置
  void _loadSyncSettings() {
    isSyncEnabled.value = _prefs?.getBool(_syncEnabledKey) ?? true;
    final lastSyncStr = _prefs?.getString(_lastSyncTimeKey);
    if (lastSyncStr != null) {
      lastSyncTime.value = DateTime.parse(lastSyncStr);
    }
  }

  /// 设置同步开关
  Future<void> setSyncEnabled(bool enabled) async {
    isSyncEnabled.value = enabled;
    await _prefs?.setBool(_syncEnabledKey, enabled);
    
    if (enabled && _authService.isLoggedIn.value) {
      // 启用同步时立即同步一次
      await syncUserData();
    }
  }

  /// 同步用户数据
  Future<bool> syncUserData() async {
    if (!_authService.isLoggedIn.value || !isSyncEnabled.value) {
      return false;
    }

    try {
      isSyncing.value = true;
      
      // 获取本地数据
      final localData = await _collectLocalData();
      
      // 获取认证Token
      final token = await _getToken();
      if (token == null) {
        print('同步失败: 未找到认证Token');
        return false;
      }

      // 上传到服务器
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/sync/upload',
        data: {
          'data': localData,
          'timestamp': DateTime.now().toIso8601String(),
        },
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.data['success'] == true) {
        // 下载服务器数据
        await _downloadServerData();
        
        // 更新最后同步时间
        lastSyncTime.value = DateTime.now();
        await _prefs?.setString(_lastSyncTimeKey, lastSyncTime.value!.toIso8601String());
        
        Get.snackbar('成功', '数据同步完成');
        return true;
      } else {
        Get.snackbar('错误', '数据同步失败: ${response.data['message']}');
        return false;
      }
    } catch (e) {
      print('数据同步失败: $e');
      Get.snackbar('错误', '数据同步失败: ${e.toString()}');
      return false;
    } finally {
      isSyncing.value = false;
    }
  }

  /// 收集本地数据
  Future<Map<String, dynamic>> _collectLocalData() async {
    final data = <String, dynamic>{};
    
    try {
      // 收集小说数据
      try {
        final novelController = Get.find<NovelController>();
        data['novels'] = novelController.novels.map((novel) => novel.toJson()).toList();
      } catch (e) {
        print('收集小说数据失败: $e');
        data['novels'] = [];
      }

      // 收集角色卡片数据
      try {
        // 暂时跳过，等待控制器实现
        data['characterCards'] = [];
      } catch (e) {
        print('收集角色卡片数据失败: $e');
        data['characterCards'] = [];
      }

      // 收集自定义角色类型
      try {
        // 暂时跳过，等待控制器实现
        data['characterTypes'] = [];
      } catch (e) {
        print('收集角色类型数据失败: $e');
        data['characterTypes'] = [];
      }

      // 收集知识库文档
      try {
        final knowledgeController = Get.find<KnowledgeBaseController>();
        data['knowledgeDocuments'] = knowledgeController.documents.map((doc) => doc.toJson()).toList();
      } catch (e) {
        print('收集知识库数据失败: $e');
        data['knowledgeDocuments'] = [];
      }

      // 收集写作风格包
      try {
        final styleController = Get.find<WritingStylePackageController>();
        data['stylePackages'] = styleController.packages.map((pkg) => pkg.toJson()).toList();
      } catch (e) {
        print('收集风格包数据失败: $e');
        data['stylePackages'] = [];
      }

      // 收集用户设置
      data['userSettings'] = _authService.currentUser.value?.settings.toJson();

    } catch (e) {
      print('收集本地数据失败: $e');
    }
    
    return data;
  }

  /// 下载服务器数据
  Future<void> _downloadServerData() async {
    try {
      final token = await _getToken();
      if (token == null) {
        print('下载失败: 未找到认证Token');
        return;
      }

      final response = await _dio.get(
        '${ApiConfig.baseUrl}/sync/download',
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.data['success'] == true) {
        final serverData = response.data['data'];
        await _applyServerData(serverData);
      }
    } catch (e) {
      print('下载服务器数据失败: $e');
    }
  }

  /// 应用服务器数据
  Future<void> _applyServerData(Map<String, dynamic> serverData) async {
    try {
      // 应用小说数据
      if (serverData['novels'] != null) {
        try {
          final novelController = Get.find<NovelController>();
          final serverNovels = (serverData['novels'] as List)
              .map((json) => Novel.fromJson(json))
              .toList();
          await _mergeNovels(novelController, serverNovels);
        } catch (e) {
          print('应用小说数据失败: $e');
        }
      }

      // 应用角色卡片数据
      if (serverData['characterCards'] != null) {
        print('角色卡片数据应用暂时跳过');
      }

      // 应用自定义角色类型
      if (serverData['characterTypes'] != null) {
        print('角色类型数据应用暂时跳过');
      }

      // 应用知识库文档
      if (serverData['knowledgeDocuments'] != null) {
        try {
          final knowledgeController = Get.find<KnowledgeBaseController>();
          final serverDocs = (serverData['knowledgeDocuments'] as List)
              .map((json) => KnowledgeDocument.fromJson(json))
              .toList();
          await _mergeKnowledgeDocuments(knowledgeController, serverDocs);
        } catch (e) {
          print('应用知识库数据失败: $e');
        }
      }

      // 应用写作风格包
      if (serverData['stylePackages'] != null) {
        try {
          final styleController = Get.find<WritingStylePackageController>();
          final serverPackages = (serverData['stylePackages'] as List)
              .map((json) => WritingStylePackage.fromJson(json))
              .toList();
          await _mergeStylePackages(styleController, serverPackages);
        } catch (e) {
          print('应用风格包数据失败: $e');
        }
      }
      
      // 应用用户设置
      if (serverData['userSettings'] != null) {
        final settings = UserSettings.fromJson(serverData['userSettings']);
        if (_authService.currentUser.value != null) {
          _authService.currentUser.value!.settings = settings;
          // 保存到本地
          await _prefs?.setString('user_data', jsonEncode(_authService.currentUser.value!.toJson()));
        }
      }
      
    } catch (e) {
      print('应用服务器数据失败: $e');
    }
  }

  /// 合并小说数据
  Future<void> _mergeNovels(NovelController controller, List<Novel> serverNovels) async {
    // 实现数据合并逻辑，以最新更新时间为准
    for (final serverNovel in serverNovels) {
      final localIndex = controller.novels.indexWhere((n) => n.id == serverNovel.id);
      if (localIndex == -1) {
        // 本地没有，直接添加
        controller.novels.add(serverNovel);
      } else {
        // 本地有，比较更新时间
        final localNovel = controller.novels[localIndex];
        if (serverNovel.updatedAt != null &&
            (localNovel.updatedAt == null || serverNovel.updatedAt!.isAfter(localNovel.updatedAt!))) {
          controller.novels[localIndex] = serverNovel;
        }
      }
    }
    // 保存小说数据
    try {
      // 暂时跳过保存，等待控制器方法实现
      print('小说数据保存暂时跳过');
    } catch (e) {
      print('保存小说数据失败: $e');
    }
  }

  /// 合并角色卡片数据
  Future<void> _mergeCharacterCards(dynamic controller, List<CharacterCard> serverCards) async {
    // 暂时跳过，等待控制器实现
    print('角色卡片数据合并暂时跳过');
  }

  /// 合并角色类型数据
  Future<void> _mergeCharacterTypes(dynamic controller, List<CharacterType> serverTypes) async {
    // 暂时跳过，等待控制器实现
    print('角色类型数据合并暂时跳过');
  }

  /// 合并知识库文档数据
  Future<void> _mergeKnowledgeDocuments(KnowledgeBaseController controller, List<KnowledgeDocument> serverDocs) async {
    for (final serverDoc in serverDocs) {
      final localIndex = controller.documents.indexWhere((d) => d.id == serverDoc.id);
      if (localIndex == -1) {
        controller.documents.add(serverDoc);
      } else {
        final localDoc = controller.documents[localIndex];
        if (serverDoc.updatedAt.isAfter(localDoc.updatedAt)) {
          controller.documents[localIndex] = serverDoc;
        }
      }
    }
    // 保存知识库数据
    try {
      // 暂时跳过保存，等待控制器方法实现
      print('知识库数据保存暂时跳过');
    } catch (e) {
      print('保存知识库数据失败: $e');
    }
  }

  /// 合并写作风格包数据
  Future<void> _mergeStylePackages(WritingStylePackageController controller, List<WritingStylePackage> serverPackages) async {
    for (final serverPackage in serverPackages) {
      final localIndex = controller.packages.indexWhere((p) => p.id == serverPackage.id);
      if (localIndex == -1) {
        controller.packages.add(serverPackage);
      } else {
        controller.packages[localIndex] = serverPackage;
      }
    }
    // 保存风格包数据
    try {
      // 暂时跳过保存，等待控制器方法实现
      print('风格包数据保存暂时跳过');
    } catch (e) {
      print('保存风格包数据失败: $e');
    }
  }

  /// 导出用户数据
  Future<Map<String, dynamic>> exportUserData() async {
    return await _collectLocalData();
  }

  /// 导入用户数据
  Future<bool> importUserData(Map<String, dynamic> data) async {
    try {
      await _applyServerData(data);
      Get.snackbar('成功', '数据导入完成');
      return true;
    } catch (e) {
      print('数据导入失败: $e');
      Get.snackbar('错误', '数据导入失败: ${e.toString()}');
      return false;
    }
  }

  /// 获取当前用户Token
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }
}
