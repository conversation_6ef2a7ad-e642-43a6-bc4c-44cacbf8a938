# 岱宗文脉账号系统快速启动指南

## 1. 前端Flutter应用配置

### 1.1 依赖安装
```bash
# 安装Flutter依赖
flutter pub get

# 生成代码文件
flutter packages pub run build_runner build
```

### 1.2 配置API地址
编辑 `lib/config/api_config.dart`：
```dart
class ApiConfig {
  // 替换为您的腾讯云Serverless API地址
  static const String baseUrl = 'https://your-serverless-api.com';
  static const String wsUrl = 'wss://your-serverless-ws.com';
}
```

### 1.3 配置微信支付
编辑 `lib/services/payment_service.dart`：
```dart
await registerWxApi(
  appId: "your_wechat_app_id", // 替换为实际的微信AppID
  doOnAndroid: true,
  doOnIOS: true,
  universalLink: "your_universal_link", // iOS需要
);
```

## 2. 后端Serverless部署

### 2.1 环境准备
```bash
# 安装Serverless Framework
npm install -g serverless

# 安装腾讯云插件
npm install -g serverless-tencent-scf

# 配置腾讯云密钥
serverless config credentials --provider tencentcloud --key <your-key> --secret <your-secret>
```

### 2.2 创建项目结构
```
backend/
├── serverless.yml
├── auth/
│   ├── send_code.py
│   ├── register.py
│   └── login.py
├── payment/
│   ├── wechat.py
│   └── member_code.py
├── sync/
│   ├── upload.py
│   └── download.py
└── requirements.txt
```

### 2.3 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE novel_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行建表SQL（参考SERVERLESS_BACKEND_GUIDE.md中的数据库设计）
```

### 2.4 部署云函数
```bash
# 部署所有函数
serverless deploy

# 查看部署状态
serverless info
```

## 3. 功能测试

### 3.1 用户注册流程测试
1. 打开应用，点击"立即注册"
2. 输入用户名、手机号
3. 点击"发送验证码"，输入收到的验证码
4. 设置密码并确认
5. 可选输入会员码
6. 同意用户协议，点击"注册"

### 3.2 用户登录测试
1. 输入用户名和密码
2. 点击"登录"
3. 可选择"记住我"
4. 如果启用了生物识别，可以使用指纹/面部识别登录

### 3.3 会员购买测试
1. 进入"会员中心"
2. 选择会员套餐
3. 点击"立即购买"
4. 选择支付方式（微信支付或会员码）
5. 完成支付流程

### 3.4 数据同步测试
1. 在设置中开启"数据同步"
2. 创建小说、角色等数据
3. 点击"手动同步"
4. 在另一设备登录同一账号，验证数据是否同步

## 4. 常见问题解决

### 4.1 编译错误
```bash
# 清理缓存
flutter clean
flutter pub get

# 重新生成代码
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### 4.2 网络请求失败
- 检查API地址配置是否正确
- 确认后端服务是否正常运行
- 检查网络连接和防火墙设置

### 4.3 支付功能异常
- 确认微信支付配置是否正确
- 检查商户号和API密钥
- 验证支付回调地址配置

### 4.4 数据同步问题
- 检查用户登录状态
- 确认同步开关是否开启
- 查看网络连接和服务器状态

## 5. 开发调试

### 5.1 本地调试
```bash
# 启动Flutter应用
flutter run

# 查看日志
flutter logs
```

### 5.2 后端调试
```bash
# 查看云函数日志
serverless logs -f auth-register

# 本地测试云函数
serverless invoke local -f auth-register -d '{"body": "{\"username\":\"test\"}"}'
```

### 5.3 数据库调试
```sql
-- 查看用户数据
SELECT * FROM users WHERE username = 'test_user';

-- 查看订单状态
SELECT * FROM orders WHERE user_id = 'user_id_here';

-- 查看会员码使用情况
SELECT * FROM member_codes WHERE is_used = TRUE;
```

## 6. 生产环境配置

### 6.1 安全配置
- 更换JWT密钥为强密码
- 配置HTTPS证书
- 设置API访问限制
- 启用数据库访问控制

### 6.2 性能优化
- 配置CDN加速
- 启用Redis缓存
- 优化数据库索引
- 设置合适的云函数规格

### 6.3 监控告警
- 配置云监控告警
- 设置日志收集
- 监控关键指标
- 配置异常通知

## 7. 维护更新

### 7.1 版本更新
```bash
# 更新Flutter应用
flutter build apk --release

# 更新云函数
serverless deploy function -f function-name
```

### 7.2 数据备份
- 定期备份数据库
- 备份用户上传文件
- 保存配置文件
- 记录版本变更

### 7.3 用户支持
- 提供用户手册
- 设置客服系统
- 收集用户反馈
- 及时处理问题

## 8. 扩展功能

### 8.1 第三方登录
- 集成微信登录SDK
- 配置QQ登录
- 支持Apple ID登录

### 8.2 高级功能
- 实现推送通知
- 添加数据分析
- 支持多语言
- 优化用户体验

这个快速启动指南帮助您快速部署和测试岱宗文脉的账号系统。如果遇到问题，请参考详细的技术文档或联系技术支持。
